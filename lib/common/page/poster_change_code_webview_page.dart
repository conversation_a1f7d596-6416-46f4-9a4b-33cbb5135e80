import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/page/mine/team_manager/team_manager_nav.dart';
import 'package:permission_handler/permission_handler.dart';

import '../dialog/custom_dialog.dart';

class PosterChangeCodeWebView extends StatefulWidget {
  final String url;
  final String? title;
  const PosterChangeCodeWebView({super.key, required this.url, this.title});

  @override
  createState() => _PosterChangeCodeWebViewState();
}

class _PosterChangeCodeWebViewState extends State<PosterChangeCodeWebView>
    with TickerProviderStateMixin {
  final GlobalKey webViewKey = GlobalKey();

  String url = '';
  double progress = 0;
  InAppWebViewController? webViewController;

  // 手势和动画相关变量
  late AnimationController _slideAnimationController;
  late Animation<Offset> _slideAnimation;
  bool _isAnimating = false;
  double _dragStartX = 0;
  double _currentDragX = 0;
  bool _isDragging = false;
  double _dragProgress = 0.0; // 拖拽进度，用于显示视觉反馈
  bool _isGoingBack = false; // 标记是否正在执行返回操作
  @override
  void initState() {
    super.initState();
    url = widget.url;

    // 初始化动画控制器
    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 初始化为无偏移的动画，具体方向在使用时设置
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeInOut,
    ));
    Permission.storage.status.then((storageStatus) {
      if (!storageStatus.isGranted) {
        if (Platform.isAndroid) {
          showDialog(
            context: context,
            builder: (context) => CustomDialog(
              title: "提示",
              content: "保存海报到本地需要存储权限",
              cancelButtonText: "取消",
              confirmButtonText: "确定",
              cancelButtonColor: AppTheme.colorButtonGrey,
              confirmButtonColor: AppTheme.colorBlue,
              onCancel: () {
                Navigator.of(context).pop();
              },
              onConfirm: () async {
                Navigator.of(context).pop();
                _requestStoragePermission();
              },
            ),
          );
        } else {
          _requestStoragePermission();
        }
      } else {
        _requestStoragePermission();
      }
    });
  }

  @override
  void dispose() {
    _slideAnimationController.dispose();
    super.dispose();
  }

  // 处理水平拖拽开始
  void _onPanStart(DragStartDetails details) {
    if (_isAnimating) return;
    _dragStartX = details.globalPosition.dx;
    _currentDragX = _dragStartX;
    _isDragging = true;
  }

  // 处理水平拖拽更新
  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isDragging || _isAnimating) return;

    _currentDragX = details.globalPosition.dx;
    double deltaX = _currentDragX - _dragStartX;

    // 限制拖拽范围，只允许向左或向右拖拽一定距离
    double screenWidth = MediaQuery.of(context).size.width;
    _dragProgress = (deltaX / screenWidth).clamp(-1.0, 1.0);

    // 实时更新页面位置，让页面跟随手势移动
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(_dragProgress, 0.0),
    ).animate(_slideAnimationController);

    // 设置动画值为1.0以显示当前拖拽位置
    _slideAnimationController.value = 1.0;

    setState(() {}); // 更新UI以显示拖拽反馈
  }

  // 处理水平拖拽结束
  void _onPanEnd(DragEndDetails details) async {
    if (!_isDragging || _isAnimating) return;

    _isDragging = false;
    double deltaX = _currentDragX - _dragStartX;
    double screenWidth = MediaQuery.of(context).size.width;

    // 判断拖拽距离是否足够触发页面切换
    double threshold = screenWidth * 0.3; // 30%的屏幕宽度作为阈值

    if (deltaX > threshold) {
      // 向右滑动 - 返回上一页或退出
      await _handleSwipeRight();
    } else if (deltaX < -threshold) {
      // 向左滑动 - 进入下一页
      await _handleSwipeLeft();
    } else {
      // 拖拽距离不够，回弹到原位
      _resetAnimation();
    }
  }

  // 处理向右滑动
  Future<void> _handleSwipeRight() async {
    if (webViewController == null) return;

    _isAnimating = true;
    _isGoingBack = true;
    bool canGoBack = await webViewController!.canGoBack();

    if (canGoBack) {
      // 有上一页，设置从右向左的返回动画
      _slideAnimation = Tween<Offset>(
        begin: Offset.zero,
        end: const Offset(1.0, 0.0), // 向右移动完全退出
      ).animate(CurvedAnimation(
        parent: _slideAnimationController,
        curve: Curves.easeInOut,
      ));

      // 重置动画控制器并执行动画
      _slideAnimationController.reset();
      await _slideAnimationController.forward();

      // 执行web返回
      await webViewController!.goBack();

      // 设置新页面从右向左进入的动画
      _slideAnimation = Tween<Offset>(
        begin: const Offset(-1.0, 0.0), // 从左侧进入
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _slideAnimationController,
        curve: Curves.easeInOut,
      ));

      _slideAnimationController.reset();
      await _slideAnimationController.forward();
      _resetAnimation();
    } else {
      // 没有上一页，退出网页容器
      _slideAnimation = Tween<Offset>(
        begin: Offset.zero,
        end: const Offset(1.0, 0.0),
      ).animate(CurvedAnimation(
        parent: _slideAnimationController,
        curve: Curves.easeInOut,
      ));

      _slideAnimationController.reset();
      await _slideAnimationController.forward();
      if (mounted) {
        Navigator.pop(context);
      }
    }
    _isAnimating = false;
    _isGoingBack = false;
  }

  // 处理向左滑动
  Future<void> _handleSwipeLeft() async {
    if (webViewController == null) return;

    _isAnimating = true;
    bool canGoForward = await webViewController!.canGoForward();

    if (canGoForward) {
      // 有下一页，设置向左移动的动画
      _slideAnimation = Tween<Offset>(
        begin: Offset.zero,
        end: const Offset(-1.0, 0.0), // 向左移动完全退出
      ).animate(CurvedAnimation(
        parent: _slideAnimationController,
        curve: Curves.easeInOut,
      ));

      // 重置动画控制器并执行动画
      _slideAnimationController.reset();
      await _slideAnimationController.forward();

      // 执行web前进
      await webViewController!.goForward();

      // 设置新页面从右向左进入的动画
      _slideAnimation = Tween<Offset>(
        begin: const Offset(1.0, 0.0), // 从右侧进入
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _slideAnimationController,
        curve: Curves.easeInOut,
      ));

      _slideAnimationController.reset();
      await _slideAnimationController.forward();
      _resetAnimation();
    } else {
      // 没有下一页，回弹到原位
      _resetAnimation();
    }
    _isAnimating = false;
  }

  // 重置动画到初始状态
  void _resetAnimation() {
    // 重置为无偏移状态
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimationController.reset();
    _dragProgress = 0.0;
    setState(() {});
  }

  Future<void> saveGrowUpCode(String imageUrl) async {
    if (await _requestStoragePermission()) {
      try {
        final imageData =
            await NetworkAssetBundle(Uri.parse(imageUrl)).load("");
        final bytes = imageData.buffer.asUint8List();
        final result = await ImageGallerySaver.saveImage(bytes);
        if (result['isSuccess']) {
          ToastUtils.show('增长码保存成功');
          // Fluttertoast.showToast(msg: "增长码保存成功");
        } else {
          ToastUtils.show('增长码保存失败');
          // Fluttertoast.showToast(msg: "增长码保存失败");
        }
      } catch (e) {
        print(e);
        ToastUtils.show('增长码保存失败');
        // Fluttertoast.showToast(msg: "增长码保存失败");
      }
    }
  }

  Future<bool> _requestStoragePermission() async {
    PermissionStatus status = await Permission.storage.request();
    return status.isGranted;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (!didPop) {
            webViewController?.canGoBack().then((canGoBack) {
              if (canGoBack) {
                webViewController?.goBack();
              } else {
                Navigator.pop(context);
              }
            });
          }
        },
        child: Material(
          color: Colors.white,
          child: Column(children: <Widget>[
            if (widget.title != null)
              TeamManagerNav(
                title: widget.title!,
                leftWidget: IconButton(
                  onPressed: () {
                    webViewController?.canGoBack().then((canGoBack) {
                      if (canGoBack) {
                        webViewController?.goBack();
                      } else {
                        Navigator.pop(context);
                      }
                    });
                  },
                  icon: const Icon(Icons.arrow_back_ios,
                      color: Color(0xFF000000)),
                  iconSize: 18,
                ),
              ),
            Expanded(
                child: Stack(
              children: [
                GestureDetector(
                  onPanStart: _onPanStart,
                  onPanUpdate: _onPanUpdate,
                  onPanEnd: _onPanEnd,
                  child: AnimatedBuilder(
                    animation: _slideAnimation,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(
                            _slideAnimation.value.dx *
                                MediaQuery.of(context).size.width,
                            0),
                        child: child,
                      );
                    },
                    child: InAppWebView(
                      key: webViewKey,
                      initialUrlRequest: URLRequest(url: WebUri(widget.url)),
                      initialSettings: InAppWebViewSettings(
                          javaScriptEnabled: true,
                          useOnDownloadStart: true,
                          allowsBackForwardNavigationGestures: true,
                          underPageBackgroundColor: Colors.white),
                      onWebViewCreated: (controller) async {
                        webViewController = controller;
                      },
                      onLongPressHitTestResult: (controller, hitTestResult) {
                        if (Platform.isAndroid) {
                          var growUpCodeImageUrl = hitTestResult.extra;
                          if (growUpCodeImageUrl != null &&
                              growUpCodeImageUrl.isNotEmpty) {
                            saveGrowUpCode(growUpCodeImageUrl);
                          }
                        }
                      },
                      onDownloadStartRequest: (controller,
                          DownloadStartRequest downloadStartRequest) {
                        // 注册消息处理器
                        controller.addJavaScriptHandler(
                          handlerName: 'onImageCaptured',
                          callback: (args) async {
                            if (args.isEmpty || args[0] is! String) {
                              return;
                            }
                            String base64Data = args[0];
                            Uint8List bytes = base64Decode(base64Data);

                            if (await _requestStoragePermission()) {
                              try {
                                final result =
                                    await ImageGallerySaver.saveImage(bytes);
                                if (result['isSuccess']) {
                                  // Fluttertoast.showToast(msg: "图片保存成功");
                                } else {
                                  ToastUtils.show('图片保存失败');
                                }
                              } catch (e) {
                                print(e);
                                ToastUtils.show('图片保存失败');
                              }
                            }
                          },
                        );

                        // 注入 JavaScript 代码以获取 canvas 图像
                        String jsCode = """
          (async () => {
            const canvasElement = document.getElementById('canvas');
            if (!canvasElement) {
              window.flutter_inappwebview.callHandler('onCanvasCaptured', JSON.stringify({ success: false, message: 'Canvas element with id "canvas" not found.' }));
              return;
            }

            try {
              // 将 canvas 转换为图像数据 URL
              const dataUrl = canvasElement.toDataURL('image/png');
              window.flutter_inappwebview.callHandler('onImageCaptured', dataUrl.split(',')[1]);
            } catch (error) {
              console.error('Error capturing canvas:', error);
              window.flutter_inappwebview.callHandler('onImageCaptured', JSON.stringify({ success: false, message: 'Failed to capture canvas.' }));
            }
          })();
        """;

                        controller.evaluateJavascript(source: jsCode);
                      },
                      onLoadStart: (controller, url) async {},
                      onLoadStop: (controller, url) async {},
                      onUpdateVisitedHistory: (controller, url, isReload) {},
                      onTitleChanged: (controller, title) {},
                      onProgressChanged: (controller, progress) {
                        setState(() {
                          this.progress = progress / 100;
                        });
                      },
                    ),
                  ),
                ),
                progress < 1.0
                    ? LinearProgressIndicator(
                        value: progress,
                        color: AppTheme.colorBlue,
                      )
                    : Container(),
                // 拖拽反馈层
                if (_isDragging && _dragProgress.abs() > 0.1)
                  Positioned.fill(
                    child: Container(
                      color: Colors.black.withOpacity(0.1),
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 10),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                _dragProgress > 0
                                    ? Icons.arrow_back_ios
                                    : Icons.arrow_forward_ios,
                                color: Colors.white,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _dragProgress > 0 ? '返回上一页' : '进入下一页',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            )),
          ]),
        ));
  }
}
