import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 优化后的手势演示页面
/// 展示：
/// 1. 没有上一页时直接退出，不执行动画
/// 2. 没有下一页时不执行动画
/// 3. 避免页面切换时的空白问题
class OptimizedGestureDemoPage extends StatefulWidget {
  const OptimizedGestureDemoPage({super.key});

  @override
  State<OptimizedGestureDemoPage> createState() => _OptimizedGestureDemoPageState();
}

class _OptimizedGestureDemoPageState extends State<OptimizedGestureDemoPage>
    with TickerProviderStateMixin {
  // 手势和动画相关变量
  late AnimationController _slideAnimationController;
  late Animation<Offset> _slideAnimation;
  bool _isAnimating = false;
  double _dragStartX = 0;
  double _currentDragX = 0;
  bool _isDragging = false;
  double _dragProgress = 0.0;
  double _lastUpdateTime = 0;
  bool _hasTriggeredThresholdFeedback = false;
  
  // 模拟页面数据
  int _currentPageIndex = 1;
  final List<String> _pages = ['页面 0', '页面 1', '页面 2', '页面 3'];

  @override
  void initState() {
    super.initState();
    
    // 初始化动画控制器
    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 初始化为无偏移的动画
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _slideAnimationController.dispose();
    super.dispose();
  }

  // 检查是否可以返回
  bool get _canGoBack => _currentPageIndex > 0;
  
  // 检查是否可以前进
  bool get _canGoForward => _currentPageIndex < _pages.length - 1;

  // 获取拖拽反馈文本
  String _getDragFeedbackText() {
    if (_dragProgress > 0) {
      return _canGoBack ? '返回上一页' : '退出页面';
    } else {
      return _canGoForward ? '进入下一页' : '已是最后一页';
    }
  }

  // 处理水平拖拽开始
  void _onPanStart(DragStartDetails details) {
    if (_isAnimating) return;
    _dragStartX = details.globalPosition.dx;
    _currentDragX = _dragStartX;
    _isDragging = true;
    _hasTriggeredThresholdFeedback = false;
    
    HapticFeedback.lightImpact();
  }

  // 处理水平拖拽更新
  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isDragging || _isAnimating) return;

    // 防抖优化
    double currentTime = DateTime.now().millisecondsSinceEpoch.toDouble();
    if (currentTime - _lastUpdateTime < 16) return;
    _lastUpdateTime = currentTime;

    _currentDragX = details.globalPosition.dx;
    double deltaX = _currentDragX - _dragStartX;
    double screenWidth = MediaQuery.of(context).size.width;
    double newDragProgress = (deltaX / screenWidth).clamp(-1.0, 1.0);
    
    if ((newDragProgress - _dragProgress).abs() < 0.01) return;
    _dragProgress = newDragProgress;

    // 阈值反馈
    double threshold = 0.3;
    bool reachedThreshold = _dragProgress.abs() >= threshold;
    
    if (reachedThreshold && !_hasTriggeredThresholdFeedback) {
      HapticFeedback.selectionClick();
      _hasTriggeredThresholdFeedback = true;
    } else if (!reachedThreshold && _hasTriggeredThresholdFeedback) {
      _hasTriggeredThresholdFeedback = false;
    }

    // 实时更新页面位置
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(_dragProgress, 0.0),
    ).animate(_slideAnimationController);
    
    _slideAnimationController.value = 1.0;
    setState(() {});
  }

  // 处理水平拖拽结束
  void _onPanEnd(DragEndDetails details) async {
    if (!_isDragging || _isAnimating) return;

    _isDragging = false;
    double deltaX = _currentDragX - _dragStartX;
    double screenWidth = MediaQuery.of(context).size.width;
    double threshold = screenWidth * 0.3;

    if (deltaX > threshold) {
      HapticFeedback.mediumImpact();
      await _handleSwipeRight();
    } else if (deltaX < -threshold) {
      HapticFeedback.mediumImpact();
      await _handleSwipeLeft();
    } else {
      HapticFeedback.lightImpact();
      _resetAnimation();
    }
  }

  // 处理向右滑动（优化版）
  Future<void> _handleSwipeRight() async {
    _isAnimating = true;
    
    if (_canGoBack) {
      // 有上一页，直接切换页面避免空白
      _currentPageIndex--;
      
      // 执行从右向左的返回动画效果
      _slideAnimation = Tween<Offset>(
        begin: const Offset(1.0, 0.0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _slideAnimationController,
        curve: Curves.easeInOut,
      ));

      _slideAnimationController.reset();
      await _slideAnimationController.forward();
      _resetAnimation();
    } else {
      // 没有上一页，直接"退出"（这里模拟退出效果）
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('已退出页面')),
        );
      }
      _resetAnimation();
    }
    _isAnimating = false;
  }

  // 处理向左滑动（优化版）
  Future<void> _handleSwipeLeft() async {
    _isAnimating = true;
    
    if (_canGoForward) {
      // 有下一页，直接切换页面避免空白
      _currentPageIndex++;
      
      // 执行从左向右的前进动画效果
      _slideAnimation = Tween<Offset>(
        begin: const Offset(-1.0, 0.0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _slideAnimationController,
        curve: Curves.easeInOut,
      ));

      _slideAnimationController.reset();
      await _slideAnimationController.forward();
      _resetAnimation();
    } else {
      // 没有下一页，不执行动画，直接回弹
      _resetAnimation();
    }
    _isAnimating = false;
  }

  // 重置动画
  void _resetAnimation() {
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimationController.reset();
    _dragProgress = 0.0;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('优化后手势演示'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Stack(
        children: [
          GestureDetector(
            onPanStart: _onPanStart,
            onPanUpdate: _onPanUpdate,
            onPanEnd: _onPanEnd,
            child: AnimatedBuilder(
              animation: _slideAnimation,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(
                    _slideAnimation.value.dx * MediaQuery.of(context).size.width,
                    0,
                  ),
                  child: child,
                );
              },
              child: Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.white,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _pages[_currentPageIndex],
                        style: const TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(height: 20),
                      Text(
                        '页面 ${_currentPageIndex + 1} / ${_pages.length}',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 40),
                      const Text(
                        '✨ 优化特性：\n• 没有上一页时直接退出\n• 没有下一页时不执行动画\n• 避免页面切换空白',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.black54,
                        ),
                      ),
                      const SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.arrow_back_ios,
                            color: _canGoBack ? Colors.green : Colors.grey,
                          ),
                          const SizedBox(width: 20),
                          Icon(
                            Icons.arrow_forward_ios,
                            color: _canGoForward ? Colors.green : Colors.grey,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          
          // 拖拽反馈层（优化版）
          if (_isDragging && _dragProgress.abs() > 0.1)
            Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(0.1 * _dragProgress.abs()),
                child: Center(
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 100),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 12),
                    decoration: BoxDecoration(
                      color: _hasTriggeredThresholdFeedback 
                          ? Colors.green.withOpacity(0.9)
                          : Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _dragProgress > 0
                                  ? Icons.arrow_back_ios
                                  : Icons.arrow_forward_ios,
                              color: Colors.white,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _getDragFeedbackText(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        // 进度条
                        Container(
                          width: 100,
                          height: 4,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(2),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: (_dragProgress.abs() / 0.3).clamp(0.0, 1.0),
                            child: Container(
                              decoration: BoxDecoration(
                                color: _hasTriggeredThresholdFeedback 
                                    ? Colors.white
                                    : Colors.green,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

// 演示应用入口
void main() {
  runApp(const OptimizedGestureDemoApp());
}

class OptimizedGestureDemoApp extends StatelessWidget {
  const OptimizedGestureDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '优化后手势演示',
      theme: ThemeData(
        primarySwatch: Colors.green,
        useMaterial3: true,
      ),
      home: const OptimizedGestureDemoPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}
