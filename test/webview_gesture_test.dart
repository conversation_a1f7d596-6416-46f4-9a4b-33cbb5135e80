import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:npemployee/common/page/poster_change_code_webview_page.dart';

void main() {
  group('WebView手势优化测试', () {
    testWidgets('测试左右拖拽方向一致性', (WidgetTester tester) async {
      // 构建测试页面
      await tester.pumpWidget(
        MaterialApp(
          home: const PosterChangeCodeWebView(
            url: 'https://www.example.com',
            title: '测试页面',
          ),
        ),
      );

      // 等待页面加载
      await tester.pumpAndSettle();

      // 查找GestureDetector
      final gestureDetector = find.byType(GestureDetector);
      expect(gestureDetector, findsOneWidget);

      // 模拟向右拖拽手势（从左向右）
      await tester.drag(gestureDetector, const Offset(100, 0));
      await tester.pumpAndSettle();

      // 验证页面向右移动（正方向）
      // 这里应该检查Transform.translate的offset值

      // 模拟向左拖拽手势（从右向左）
      await tester.drag(gestureDetector, const Offset(-100, 0));
      await tester.pumpAndSettle();

      // 验证页面向左移动（负方向）
    });

    testWidgets('测试返回动画方向', (WidgetTester tester) async {
      // 构建测试页面
      await tester.pumpWidget(
        MaterialApp(
          home: const PosterChangeCodeWebView(
            url: 'https://www.example.com',
            title: '测试页面',
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 模拟足够大的向右拖拽以触发返回
      final gestureDetector = find.byType(GestureDetector);
      await tester.drag(gestureDetector, const Offset(200, 0));
      await tester.pumpAndSettle();

      // 验证返回动画是从右向左
      // 这里应该检查动画的方向和效果
    });

    testWidgets('测试拖拽反馈UI显示', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const PosterChangeCodeWebView(
            url: 'https://www.example.com',
            title: '测试页面',
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 开始拖拽
      final gestureDetector = find.byType(GestureDetector);
      final TestGesture gesture = await tester.startGesture(
        tester.getCenter(gestureDetector),
      );

      // 拖拽一定距离
      await gesture.moveBy(const Offset(50, 0));
      await tester.pump();

      // 验证反馈UI显示
      expect(find.text('返回上一页'), findsOneWidget);
      expect(find.byIcon(Icons.arrow_back_ios), findsOneWidget);

      // 向左拖拽
      await gesture.moveBy(const Offset(-100, 0));
      await tester.pump();

      // 验证反馈UI更新
      expect(find.text('进入下一页'), findsOneWidget);
      expect(find.byIcon(Icons.arrow_forward_ios), findsOneWidget);

      // 结束拖拽
      await gesture.up();
      await tester.pumpAndSettle();
    });
  });
}
